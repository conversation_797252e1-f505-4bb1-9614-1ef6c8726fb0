import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Dynamic import to reduce initial bundle size
const BudgetPlanningPage = dynamic(
  () => import('@/components/accounting/budget/budget-planning-page').then(mod => ({ default: mod.BudgetPlanningPage })),
  {
    loading: () => (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-muted-foreground">Loading Budget Planning...</p>
        </div>
      </div>
    ),
    ssr: false
  }
);

export const metadata = {
  title: 'Budget Planning',
  description: 'Create and manage budget plans for the Teachers Council of Malawi.',
};

export default function BudgetPlanningRoute() {
  return <BudgetPlanningPage />;
}
