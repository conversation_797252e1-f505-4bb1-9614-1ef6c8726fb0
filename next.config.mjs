/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
    domains: ['avatars.githubusercontent.com', 'images.unsplash.com'],
  },
  serverExternalPackages: [
    'mongoose',
    'pdfkit',
    'archiver',
    'xlsx',
    'jspdf',
    'pdfmake',
    'html2canvas',
    'qrcode',
    'jsbarcode',
    'crypto-js',
    'bcryptjs',
    'jsonwebtoken',
    'ua-parser-js',
    'csv-parse',
    'json2csv',
    'xmldom',
    // Additional packages for bundle optimization
    'sharp',
    'canvas',
    'puppeteer',
    'playwright'
  ],
  reactStrictMode: true,

  // Optimize for Vercel deployment
  output: 'standalone',
  poweredByHeader: false,

  // Configure output file tracing root
  outputFileTracingRoot: process.cwd(),

  // Aggressive bundle optimization
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Configure API routes to use minimal runtime
  experimental: {
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      'date-fns'
    ]
  },

  // Webpack optimizations for smaller bundles
  webpack: (config, { isServer }) => {
    // Exclude large files from client bundle
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        stream: false,
        buffer: false,
        util: false,
        url: false,
        querystring: false,
        // Additional fallbacks for new systems
        os: false,
        child_process: false,
        net: false,
        tls: false,
        dns: false,
      };

      // Exclude heavy analytics and AI services from client bundle
      config.resolve.alias = {
        ...config.resolve.alias,
        '@/lib/services/accounting/ai-insights-engine': false,
        '@/lib/services/accounting/ml-models-service': false,
        '@/lib/services/accounting/predictive-analytics-service': false,
        '@/lib/services/accounting/advanced-ai-models': false,
      };
    }

    // Aggressive tree shaking
    config.optimization = config.optimization || {};
    config.optimization.usedExports = true;
    config.optimization.sideEffects = false;

    // Optimize bundle size for server functions
    if (isServer) {
      // External packages that should not be bundled
      config.externals = config.externals || [];
      config.externals.push({
        'xlsx': 'commonjs xlsx',
        'jspdf': 'commonjs jspdf',
        'pdfkit': 'commonjs pdfkit',
        'pdfmake': 'commonjs pdfmake',
        'archiver': 'commonjs archiver',
        'html2canvas': 'commonjs html2canvas',
        'qrcode': 'commonjs qrcode',
        'jsbarcode': 'commonjs jsbarcode',
        'crypto-js': 'commonjs crypto-js',
        'bcryptjs': 'commonjs bcryptjs',
        'jsonwebtoken': 'commonjs jsonwebtoken',
        'ua-parser-js': 'commonjs ua-parser-js',
        'csv-parse': 'commonjs csv-parse',
        'json2csv': 'commonjs json2csv',
        'xmldom': 'commonjs xmldom',
        'mongoose': 'commonjs mongoose'
      });

      // Split chunks for better optimization
      config.optimization = config.optimization || {};
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        chunks: 'all',
        maxSize: 150000, // 150KB limit for more aggressive splitting
        minSize: 10000,  // 10KB minimum chunk size
        cacheGroups: {
          ...config.optimization.splitChunks?.cacheGroups,
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            maxSize: 150000, // 150KB limit for vendor chunks
            priority: 10,
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            maxSize: 100000, // 100KB limit for common chunks
            priority: 5,
          },
          default: {
            minChunks: 2,
            priority: -10,
            reuseExistingChunk: true,
            maxSize: 100000,
          },
        },
      };
    }

    // Fix exports issue
    config.module.rules.push({
      test: /\.m?js$/,
      resolve: {
        fullySpecified: false,
      },
    });

    return config;
  }
}

export default nextConfig
