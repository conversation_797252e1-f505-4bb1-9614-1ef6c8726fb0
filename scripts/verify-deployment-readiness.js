#!/usr/bin/env node

/**
 * Deployment Readiness Verification Script
 * Checks if the project is optimized and ready for Vercel deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Verifying deployment readiness...\n');

// Configuration
const MAX_PROJECT_SIZE_MB = 150;
const MAX_BUILD_SIZE_MB = 60;
const CRITICAL_ROUTES = [
  'app/(dashboard)/dashboard/accounting/budget/planning/page.tsx',
  'app/(dashboard)/dashboard/page.tsx',
  'app/(dashboard)/dashboard/accounting/page.tsx'
];

let issues = [];
let warnings = [];

// 1. Check project size (excluding node_modules as Vercel installs dependencies)
console.log('📏 Checking project size (excluding node_modules)...');
try {
  const sizeOutput = execSync('du -sm --exclude=node_modules .', { encoding: 'utf8' }).trim();
  const projectSizeMB = parseInt(sizeOutput.split('\t')[0]);

  if (projectSizeMB > MAX_PROJECT_SIZE_MB) {
    issues.push(`Project size (${projectSizeMB}MB) exceeds recommended limit (${MAX_PROJECT_SIZE_MB}MB)`);
  } else {
    console.log(`✅ Project size: ${projectSizeMB}MB (within limits, excluding node_modules)`);
  }
} catch (error) {
  // Fallback for systems that don't support --exclude
  try {
    const totalSize = execSync('du -sm .', { encoding: 'utf8' }).trim();
    const nodeModulesSize = fs.existsSync('node_modules') ?
      execSync('du -sm node_modules', { encoding: 'utf8' }).trim() : '0\t';

    const totalMB = parseInt(totalSize.split('\t')[0]);
    const nodeModulesMB = parseInt(nodeModulesSize.split('\t')[0]);
    const projectSizeMB = totalMB - nodeModulesMB;

    if (projectSizeMB > MAX_PROJECT_SIZE_MB) {
      issues.push(`Project size (${projectSizeMB}MB) exceeds recommended limit (${MAX_PROJECT_SIZE_MB}MB)`);
    } else {
      console.log(`✅ Project size: ${projectSizeMB}MB (within limits, excluding node_modules)`);
    }
  } catch (fallbackError) {
    warnings.push('Could not determine project size');
  }
}

// 2. Check build output size
console.log('\n📦 Checking build output...');
if (fs.existsSync('.next')) {
  try {
    const buildSizeOutput = execSync('du -sm .next', { encoding: 'utf8' }).trim();
    const buildSizeMB = parseInt(buildSizeOutput.split('\t')[0]);
    
    if (buildSizeMB > MAX_BUILD_SIZE_MB) {
      issues.push(`Build size (${buildSizeMB}MB) exceeds recommended limit (${MAX_BUILD_SIZE_MB}MB)`);
    } else {
      console.log(`✅ Build output: ${buildSizeMB}MB (within limits)`);
    }
  } catch (error) {
    warnings.push('Could not determine build size');
  }
} else {
  warnings.push('No build output found. Run "pnpm run build" first.');
}

// 3. Check critical routes exist
console.log('\n🛣️  Checking critical routes...');
CRITICAL_ROUTES.forEach(route => {
  if (fs.existsSync(route)) {
    console.log(`✅ ${route}`);
  } else {
    issues.push(`Missing critical route: ${route}`);
  }
});

// 4. Check for heavy development files
console.log('\n🧹 Checking for development files...');
const heavyDevFiles = [
  'scripts/ts-checker/node_modules',
  'ENTERPRISE_MULTI_TENANT_IMPLEMENTATION_GUIDE.pdf',
  'stores/attendance',
  'stores/task'
];

let foundHeavyFiles = [];
heavyDevFiles.forEach(file => {
  if (fs.existsSync(file)) {
    foundHeavyFiles.push(file);
  }
});

if (foundHeavyFiles.length > 0) {
  warnings.push(`Found development files that should be excluded: ${foundHeavyFiles.join(', ')}`);
} else {
  console.log('✅ No heavy development files found');
}

// 5. Check .vercelignore configuration
console.log('\n📋 Checking .vercelignore...');
if (fs.existsSync('.vercelignore')) {
  const vercelIgnore = fs.readFileSync('.vercelignore', 'utf8');
  const requiredPatterns = ['scripts/', '*_IMPLEMENTATION_GUIDE.md', '**/*-backup.tsx'];
  
  let missingPatterns = [];
  requiredPatterns.forEach(pattern => {
    if (!vercelIgnore.includes(pattern)) {
      missingPatterns.push(pattern);
    }
  });
  
  if (missingPatterns.length > 0) {
    warnings.push(`Missing .vercelignore patterns: ${missingPatterns.join(', ')}`);
  } else {
    console.log('✅ .vercelignore properly configured');
  }
} else {
  issues.push('Missing .vercelignore file');
}

// 6. Check dynamic imports in budget planning
console.log('\n⚡ Checking dynamic imports...');
const budgetPlanningFile = 'components/accounting/budget/budget-planning.tsx';
if (fs.existsSync(budgetPlanningFile)) {
  const content = fs.readFileSync(budgetPlanningFile, 'utf8');
  
  if (content.includes('dynamic(') && content.includes('ssr: false')) {
    console.log('✅ Dynamic imports configured in budget planning');
  } else {
    warnings.push('Budget planning may not have proper dynamic imports');
  }
} else {
  issues.push('Budget planning component not found');
}

// 7. Check package.json build configuration
console.log('\n⚙️  Checking build configuration...');
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (packageJson.scripts && packageJson.scripts['vercel-build']) {
    if (packageJson.scripts['vercel-build'].includes('NODE_OPTIONS')) {
      console.log('✅ Build script optimized for memory usage');
    } else {
      warnings.push('Build script could benefit from memory optimization');
    }
  } else {
    issues.push('Missing vercel-build script in package.json');
  }
} else {
  issues.push('Missing package.json file');
}

// 8. Check Next.js configuration
console.log('\n🔧 Checking Next.js configuration...');
if (fs.existsSync('next.config.mjs')) {
  const nextConfig = fs.readFileSync('next.config.mjs', 'utf8');
  
  if (nextConfig.includes('serverExternalPackages') && nextConfig.includes('maxSize')) {
    console.log('✅ Next.js configuration optimized');
  } else {
    warnings.push('Next.js configuration could be more optimized');
  }
} else {
  issues.push('Missing next.config.mjs file');
}

// Results summary
console.log('\n' + '='.repeat(60));
console.log('📊 DEPLOYMENT READINESS REPORT');
console.log('='.repeat(60));

if (issues.length === 0 && warnings.length === 0) {
  console.log('🎉 EXCELLENT! Your project is fully optimized and ready for deployment.');
  console.log('\n✅ All checks passed');
  console.log('✅ No critical issues found');
  console.log('✅ No warnings detected');
  console.log('\n🚀 You can proceed with Vercel deployment confidently!');
  process.exit(0);
} else {
  if (issues.length > 0) {
    console.log('\n❌ CRITICAL ISSUES FOUND:');
    issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS:');
    warnings.forEach((warning, index) => {
      console.log(`   ${index + 1}. ${warning}`);
    });
  }
  
  if (issues.length > 0) {
    console.log('\n🔧 Please fix the critical issues before deploying.');
    process.exit(1);
  } else {
    console.log('\n✅ No critical issues, but consider addressing warnings for optimal performance.');
    console.log('🚀 You can proceed with deployment, but monitor performance.');
    process.exit(0);
  }
}
