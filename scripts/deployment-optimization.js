#!/usr/bin/env node

/**
 * Deployment Optimization Script
 * Cleans up development files and optimizes for Vercel deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Starting deployment optimization...');

// Files and directories to remove for deployment optimization
const itemsToRemove = [
  // Heavy scripts directory items (keep only essential)
  'scripts/ts-checker/node_modules',
  'scripts/ts-checker/package-lock.json',
  'scripts/component-scanner.ts',
  'scripts/typescript-error-scanner.ts',
  'scripts/ts-error-finder.ts',
  'scripts/ts-error-fixer.ts',
  'scripts/ts-error-scanner.ts',
  
  // Implementation guides and documentation
  'ENTERPRISE_MULTI_TENANT_IMPLEMENTATION_GUIDE.pdf',
  'ATTENDANCE_AND_TASK_MANAGEMENT_IMPLEMENTATION_GUIDE.md',
  'RECRUITMENT_SYSTEM_IMPLEMENTATION_GUIDE.md',
  'ACCOUNTING_REPORTS_IMPLEMENTATION_GUIDE.md',
  'ADVANCED_ANALYTICS_IMPLEMENTATION_SUMMARY.md',
  'IMPLEMENTATION_SUMMARY.md',
  'PHASE_2_IMPLEMENTATION_SUMMARY.md',
  'PHASE_3_IMPLEMENTATION_SUMMARY.md',
  'PHASE_4_IMPLEMENTATION_SUMMARY.md',
  
  // Backup and test files
  'app/(dashboard)/dashboard/accounting/assets/register/page-backup.tsx',
  'app/(dashboard)/dashboard/accounting/assets/register/page-simple.tsx',
  'app/(dashboard)/dashboard/attendance/test-modal',
  'app/(dashboard)/dashboard/test-api',
  'app/(dashboard)/dashboard/test-stores',
  
  // Development stores (not needed in production)
  'stores/attendance',
  'stores/task',
  
  // Large cache files
  'tsconfig.tsbuildinfo'
];

// Function to safely remove files/directories
function safeRemove(itemPath) {
  try {
    if (fs.existsSync(itemPath)) {
      const stats = fs.statSync(itemPath);
      if (stats.isDirectory()) {
        fs.rmSync(itemPath, { recursive: true, force: true });
        console.log(`✅ Removed directory: ${itemPath}`);
      } else {
        fs.unlinkSync(itemPath);
        console.log(`✅ Removed file: ${itemPath}`);
      }
    } else {
      console.log(`⚠️  Item not found: ${itemPath}`);
    }
  } catch (error) {
    console.error(`❌ Error removing ${itemPath}:`, error.message);
  }
}

// Remove items
console.log('\n📦 Cleaning up development files...');
itemsToRemove.forEach(item => {
  safeRemove(item);
});

// Clean up scripts directory more aggressively
console.log('\n🧹 Cleaning scripts directory...');
const scriptsDir = 'scripts';
if (fs.existsSync(scriptsDir)) {
  const scriptFiles = fs.readdirSync(scriptsDir);
  
  // Keep only essential scripts for build process
  const essentialScripts = [
    'fix-api-route-params-v2.js',
    'fix-edge-runtime-issues.js',
    'fix-route-handler-params.js',
    'vercel-prebuild.js',
    'deployment-optimization.js'
  ];
  
  scriptFiles.forEach(file => {
    const filePath = path.join(scriptsDir, file);
    if (!essentialScripts.includes(file)) {
      safeRemove(filePath);
    }
  });
}

// Check final project size
console.log('\n📊 Checking optimized project size...');
const { execSync } = require('child_process');
try {
  const size = execSync('du -sh .', { encoding: 'utf8' }).trim();
  console.log(`📏 Current project size: ${size}`);
} catch (error) {
  console.log('Could not determine project size');
}

console.log('\n✨ Deployment optimization complete!');
console.log('🎯 Ready for Vercel deployment');
