# Development files
.git
.github
.vscode
.idea
.next/cache

# Test files
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx
__tests__
__mocks__
coverage/
.nyc_output/
test-results/

# Documentation and guides (keep app routes)
README.md
CHANGELOG.md
LICENSE
docs/
public/docs/
project_guides/
*.md
DEPLOYMENT_STATUS.md
ENTITY_RECONCILIATION_PLAN.md
HR_PAYROLL_ACCOUNTING_UPDATE_TRACKER.md
TCM_ENTERPRISE_SUITE_BUSINESS_ARCHITECTURE_AND_SERVICES.md

# Development scripts and tools (keep essential scripts)
scripts/
!scripts/vercel-prebuild.js
!scripts/fix-api-route-params-v2.js
!scripts/fix-edge-runtime-issues.js
!scripts/fix-route-handler-params.js
format_excel/node_modules/

# Backup and temporary files
backups/
**/backup/
temp/
tmp/
*.backup
*.bak
*.tmp
*.temp
.DS_Store
Thumbs.db
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Large development dependencies
scripts/ts-checker/node_modules/

# Package manager files (keep pnpm-lock.yaml for consistency)
yarn.lock
package-lock.json

# Large data files (keep mock data for components)
templates/

# Implementation guides and documentation
*_IMPLEMENTATION_GUIDE.md
*_SUMMARY.md
ENTERPRISE_MULTI_TENANT_IMPLEMENTATION_GUIDE.pdf
ATTENDANCE_AND_TASK_MANAGEMENT_IMPLEMENTATION_GUIDE.md
RECRUITMENT_SYSTEM_IMPLEMENTATION_GUIDE.md

# Test and backup components
**/*-backup.tsx
**/*-simple.tsx
**/*-test.tsx
**/test-*
app/(dashboard)/dashboard/test-*
app/(dashboard)/dashboard/attendance/test-modal/

# Development stores (not needed in production)
stores/attendance/
stores/task/
